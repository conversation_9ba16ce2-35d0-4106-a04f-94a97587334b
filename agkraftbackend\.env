# MONGO_URI=mongodb://127.0.0.1:27017/form
# PORT = 8000
# EMAIL_USER=<EMAIL>
# EMAIL_PASS=ojlkatzpeepeperc
# RECIPIENT_EMAIL=

MONGO_URI=mongodb://localhost:27017/agkraftbackend
PORT=8001
# EMAIL_USER=<EMAIL>
# EMAIL_PASS=ojlkatzpeepeperc
EMAIL_USER=<EMAIL>
ADMIN_EMAIL=<EMAIL>
EMAIL_PASS=bnwfakkbyzgtbmej

# JWT Configuration for Admin Authentication
JWT_SECRET=83830498149911572491
# AES256 Encryption Key (32 characters for AES-256)
AES_SECRET_KEY=AgKraft83830498149911572491

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_FROM=<EMAIL>

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:5173

# Security Configuration
BCRYPT_SALT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=20
ACCOUNT_LOCK_TIME=1800000

# OTP Configuration
OTP_EXPIRY_TIME=600000
EMAIL_OTP_LENGTH=6

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_ATTEMPTS=20

# bucked s3
S3_BUCKET_NAME=agkraftbackend
S3_SECRET_KEY=szRILihD89bi16U6KyJVVF+zGUPM8eQGj/0QUrBn
S3_ACCESS_KEY=********************
S3_REGION=ap-southeast-2
# TWILIO_ACCOUNT_SID=**********************************
# TWILIO_AUTH_TOKEN=f616c87b3fa06cdb568da5b14b2f3bdf
# TWILIO_ACCOUNT_SID=**********************************
# TWILIO_AUTH_TOKEN=89c313307fac08ece290a84d5d1e3f04
# TWILIO_PHONE_NUMBER=your_twilio_phone_number
# TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

